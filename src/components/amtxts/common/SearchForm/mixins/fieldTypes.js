import { DateOptions } from './DateOptions'
// 字段类型与Forms组件的映射关系
export const componentMaps = {
  input: 'CustInput',
  quickSelect: 'CustSelectList',
  select: 'CustSelect',
  number: 'CustInputNumber',
  datePicker: 'CustDatePicker'
}

// 获取字段对应的组件名称
export function getComponentByType(fieldType) {
  return componentMaps[fieldType] || componentMaps.input
}

// 根据字段配置生成组件配置数据
export function generateComponentData(fieldConfig, value, operator) {
  const baseData = {
    field: fieldConfig.field,
    placeholder: fieldConfig.placeholder || '',
  }

  switch (fieldConfig.component) {
    case 'input':
      return {
        ...baseData,
        type: 'text',
        clearable: true,
        maxlength: fieldConfig.maxLength || 200
      }
      
    case 'quickSelect':
      return {
        ...baseData,
        aidName: fieldConfig.aidName || fieldConfig.field,
        multiple: fieldConfig.multiple || false,
        fillMap: fieldConfig.fillMap || ''
      }
      
    case 'select':
      return {
        ...baseData,
        values: fieldConfig.options || [],
        filterable: true,
        clearable: true
      }
      
    case 'number':
      return {
        ...baseData,
        clearable: true,
        controlsPosition: fieldConfig.controlsPosition || 'right',
        controls: fieldConfig.controls || false,
        min: fieldConfig.min,
        max: fieldConfig.max,
        precision: fieldConfig.precision || 2
      }
      
    case 'datePicker':
      return {
        ...baseData,
        format: 'daterange',
        valueFormat: 'yyyy-MM-dd',
        rangeSeparator: '-',
        align: 'center',
        unlinkPanels: true,
        pickerOptions: DateOptions,
      }
    default:
      return baseData
  }
}

// 验证字段值
export function validateFieldValue(value, fieldConfig, operator) {
  const { component, required, validation } = fieldConfig
  
  // 检查必填
  if (required && (!value || (Array.isArray(value) && value.length === 0))) {
    return {
      valid: false,
      error: `${fieldConfig.label}不能为空`
    }
  }
  
  // 如果操作符禁用输入，则不需要验证值
  if (!value && ['null', 'not_null', 'today', 'yesterday', 'last_7_days', 'last_30_days', 'this_week', 'last_week', 'this_month', 'last_month', 'this_year', 'last_year'].includes(operator)) {
    return { valid: true, error: '' }
  }
  
  // 字段特定验证
  if (validation) {
    // 长度验证
    if (validation.maxLength && value && value.length > validation.maxLength) {
      return {
        valid: false,
        error: `${fieldConfig.label}长度不能超过${validation.maxLength}个字符`
      }
    }
    
    // 正则验证
    if (validation.pattern && value && !validation.pattern.test(value)) {
      return {
        valid: false,
        error: validation.message || `${fieldConfig.label}格式不正确`
      }
    }
    
    // 数值范围验证
    if (component === 'number' && value) {
      const numValue = parseFloat(value)
      if (validation.min !== undefined && numValue < validation.min) {
        return {
          valid: false,
          error: `${fieldConfig.label}不能小于${validation.min}`
        }
      }
      if (validation.max !== undefined && numValue > validation.max) {
        return {
          valid: false,
          error: `${fieldConfig.label}不能大于${validation.max}`
        }
      }
    }
  }
  
  return { valid: true, error: '' }
}

// 格式化字段值用于提交
export function formatFieldValue(value, fieldConfig, operator) {
  if (!value) return value
  
  switch (fieldConfig.component) {
    case 'number':
      return parseFloat(value)
      
    case 'datePicker':
      if (operator === 'between' && Array.isArray(value)) {
        return value
      }
      return value
      
    case 'select':
      return value
      
    default:
      return value
  }
} 