// 操作符配置映射表
import { DateOptions } from '@/components/amtxts/common/SearchForm/mixins/DateOptions'

export const operatorMaps = {
  // 文本输入框操作符
  input: [
    { label: '类似', value: 'like', disableInput: false },
    { label: '等于', value: 'eq', disableInput: false },
    { label: '不等于', value: 'ne', disableInput: false },
    { label: '在...中', value: 'in', disableInput: false },
    { label: '不在...中', value: 'not_in', disableInput: false },
    { label: '为空', value: 'null', disableInput: true },
    { label: '不为空', value: 'not_null', disableInput: true }
  ],
  
  // 快捷查询框操作符
  quickSearch: [
    { label: '等于', value: 'eq', disableInput: false },
    { label: '不等于', value: 'ne', disableInput: false },
    { label: '类似', value: 'like', disableInput: false },
    { label: '为空', value: 'null', disableInput: true },
    { label: '不为空', value: 'not_null', disableInput: true }
  ],
  
  // 下拉选择框操作符
  select: [
    { label: '等于', value: 'eq', disableInput: false },
    { label: '不等于', value: 'ne', disableInput: false },
  ],
  
  // 数值操作符
  number: [
    { label: '等于', value: 'eq', disableInput: false },
    { label: '大于', value: 'gt', disableInput: false },
    { label: '小于', value: 'lt', disableInput: false },
    { label: '大于等于', value: 'gte', disableInput: false },
    { label: '小于等于', value: 'lte', disableInput: false },
    { label: '不等于', value: 'ne', disableInput: false },
  ],
  
  // 日期范围操作符
  datePicker: [
    { label: '起止', value: 'between', disableInput: false },
    { label: '今天', value: 'today', disableInput: true, },
    { label: '昨天', value: 'yesterday', disableInput: true },
    { label: '本周', value: 'thisWeek', disableInput: true },
    { label: '上周', value: 'lastWeek', disableInput: true },
    { label: '近7天', value: 'last7Days', disableInput: true },
    { label: '本月', value: 'thisMonth', disableInput: true },
    { label: '上月', value: 'lastMonth', disableInput: true },
    { label: '近30天', value: 'last30Days', disableInput: true },
    { label: '今年', value: 'thisYear', disableInput: true },
    { label: '去年', value: 'lastYear', disableInput: true }
  ]
}
// 获取日期操作符对于的值
export function getOperatorValue(operator) {
  const operators = getOperatorsByType('datePicker')
  const operatorObj = operators.find(op => op.value === operator)
  if(operatorObj && DateOptions[operatorObj.value]){
    return DateOptions[operatorObj.value]()
  }
  return null
}

// 获取指定字段类型的操作符
export function getOperatorsByType(fieldType) {
  return operatorMaps[fieldType] || operatorMaps.input
}

// 获取默认操作符
export function getDefaultOperator(fieldType) {
  const operators = getOperatorsByType(fieldType)
  return operators.length > 0 ? operators[0].value : ''
}

// 检查操作符是否禁用输入
export function isOperatorDisableInput(fieldType, operatorValue) {
  const operators = getOperatorsByType(fieldType)
  const operator = operators.find(op => op.value === operatorValue)
  return operator ? operator.disableInput : false
}
